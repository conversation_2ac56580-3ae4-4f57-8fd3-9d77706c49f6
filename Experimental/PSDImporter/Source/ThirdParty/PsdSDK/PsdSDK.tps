<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>psd_sdk  </Name>
  <!-- Software Name and Version  -->
  <!-- Software Name: psd_sdk 
    Version: unversioned-->
<Location>Engine/Plugins/Experimental/PhotoshopIO</Location>
  <Function>Handles the import (and  export) of Adobe Photoshop files</Function>
  <Eula>https://github.com/MolecularMatters/psd_sdk/blob/master/LICENSE</Eula>
  <RedistributeTo>
    <EndUserGroup>Licencees</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>/Engine/Source/ThirdParty/Licenses</LicenseFolder>
</TpsData>
 


 