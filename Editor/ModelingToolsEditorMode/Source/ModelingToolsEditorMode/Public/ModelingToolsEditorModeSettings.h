// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "Engine/DeveloperSettings.h"
#include "ModelingToolsEditorModeSettings.generated.h"

struct FCollectionReference;



UENUM()
enum class EModelingModeDefaultMeshObjectType
{
	/** Generate a new Static Mesh Asset (using Generated Asset settings below) and AStaticMeshActor */
	StaticMeshAsset,
	/** Generate a new AVolume Actor */
	VolumeActor,
	/** Generate a new ADynamicMeshActor (stored locally in the Level) */
	DynamicMeshActor
};



UENUM()
enum class EModelingModeAssetGenerationBehavior
{
	/** Generate and automatically Save new Assets on creation */
	AutoGenerateAndAutosave,

	/** Generate new Assets and mark as Modified but do not automatically Save */
	AutoGenerateButDoNotAutosave,

	/** Prompt to Save each new Asset upon Creation */
	InteractivePromptToSave
};



UENUM()
enum class EModelingModeAssetGenerationLocation
{
	/** All generated assets will be stored in an AutoGenerated folder that is located relative to the World they are being saved in */
	AutoGeneratedWorldRelativeAssetPath,

	/** All generated assets will be stored in a root-level AutoGenerated folder */
	AutoGeneratedGlobalAssetPath,

	/** Generated assets will be stored in the currently-visible Asset Browser folder if available, otherwise at the Auto Generated Asset Path */
	CurrentAssetBrowserPathIfAvailable
};


/**
 * Settings for the Modeling Tools Editor Mode plug-in.
 */
UCLASS(config=Editor)
class MODELINGTOOLSEDITORMODE_API UModelingToolsEditorModeSettings : public UDeveloperSettings
{
	GENERATED_BODY()

public:

	// UDeveloperSettings overrides

	virtual FName GetContainerName() const override { return FName("Project"); }
	virtual FName GetCategoryName() const override { return FName("Plugins"); }
	virtual FName GetSectionName() const override { return FName("ModelingMode"); }

	virtual FText GetSectionText() const override;
	virtual FText GetSectionDescription() const override;

protected:

	/** Enable/Disable the options to emit Dynamic Mesh Actors in Modeling Mode Tools */
	UPROPERTY()
	bool bEnableDynamicMeshActors = false;

	/** Where should Assets auto-generated by Modeling Tools be stored by default */
	UPROPERTY(config, EditAnywhere, Category = "Modeling Mode|Generated Assets")
	EModelingModeAssetGenerationLocation AssetGenerationLocation = EModelingModeAssetGenerationLocation::AutoGeneratedWorldRelativeAssetPath;

	/** How should Assets auto-generated by Modeling Tools be handled in terms of saving, naming, etc */
	UPROPERTY(config, EditAnywhere, Category = "Modeling Mode|Generated Assets")
	EModelingModeAssetGenerationBehavior AssetGenerationMode = EModelingModeAssetGenerationBehavior::AutoGenerateButDoNotAutosave;

public:

	EModelingModeAssetGenerationLocation GetAssetGenerationLocation() const
	{
		return bRestrictiveMode
				   ? EModelingModeAssetGenerationLocation::AutoGeneratedGlobalAssetPath
				   : AssetGenerationLocation;
	}

	void SetAssetGenerationLocation(const EModelingModeAssetGenerationLocation Location)
	{
		AssetGenerationLocation = Location;
	}

	EModelingModeAssetGenerationBehavior GetAssetGenerationMode() const
	{
		return bRestrictiveMode
					? EModelingModeAssetGenerationBehavior::AutoGenerateButDoNotAutosave
					: AssetGenerationMode;
	}

	void SetAssetGenerationMode(const EModelingModeAssetGenerationBehavior Mode)
	{
		AssetGenerationMode = Mode;
	}

	/** What type of Mesh Object should Output Type Setting default to in Modeling Mode Tools (takes effect after Editor restart) */
	UPROPERTY(config, EditAnywhere, Category = "Modeling Mode")
	EModelingModeDefaultMeshObjectType DefaultMeshObjectType = EModelingModeDefaultMeshObjectType::StaticMeshAsset;

	/** Assets auto-generated by Modeling Tools are stored at this path, relative to the parent path defined by the Location. Set to an empty string to disable. */
	UPROPERTY(config, EditAnywhere, Category = "Modeling Mode|Generated Assets")
	FString AutoGeneratedAssetPath = TEXT("_GENERATED");

	/** If true, Auto-Generated Assets created in an unsaved Level will be stored relative to top-level folder, otherwise they will be stored in /Temp and cannot be saved until they are explicitly moved to a permanent location */
	UPROPERTY(config, EditAnywhere, Category = "Modeling Mode|Generated Assets")
	bool bStoreUnsavedLevelAssetsInTopLevelGameFolder = true;

	/** If true, Autogenerated Assets are stored in per-user folders below the Autogen path */
	UPROPERTY(config, EditAnywhere, Category = "Modeling Mode|Generated Assets")
	bool bUsePerUserAutogenSubfolder = true;

	/** Overrides the user name used for per-user folders below the Autogen path. This might be necessary to resolve issues with source control, for example. Note that the per-user folder name might not contain the name exactly as provided. */
	UPROPERTY(config, EditAnywhere, Category = "Modeling Mode|Generated Assets")
	FString AutogenSubfolderUserNameOverride;

	/** If true, Autogenerated Assets have a short random string generated and appended to their name */
	UPROPERTY(config, EditAnywhere, Category = "Modeling Mode|Generated Assets")
	bool bAppendRandomStringToName = true;

	//
	// User Interface
	//

	/** If true, the standard UE Editor Gizmo Mode (ie selected via the Level Editor Viewport toggle) will be used to configure the Modeling Gizmo, otherwise a Combined Gizmo will always be used. It may be necessary to exit and re-enter Modeling Mode after changing this setting. */
	UPROPERTY(config, EditAnywhere, Category = "Modeling Mode|User Interface")
	bool bRespectLevelEditorGizmoMode = false;

	//
	// Selection
	//

	//~old preference for mesh selection system, to be disabled when we switch to default-enabled mesh element selection
	//UPROPERTY(config, EditAnywhere, Category = "Modeling Mode|Selection", meta = (DisplayName="Enable Mesh Selection UI"))
	UPROPERTY()
	bool bEnablePersistentSelections = false;

	//~new default-enabled preference for the mesh selection system
	/** Enable/Disable the Mesh Selection System. */
	UPROPERTY(config, EditAnywhere, Category = "Modeling Mode|Selection", meta = (DisplayName="Enable Mesh Selection UI"))
	bool bEnableMeshSelections = true;

	virtual bool GetMeshSelectionsEnabled() const
	{
		return bEnableMeshSelections;
	}
	
	virtual void SetMeshSelectionsEnabled(bool bEnabled)
	{
		bEnableMeshSelections = bEnabled;
	}


	DECLARE_MULTICAST_DELEGATE_TwoParams(UModelingToolsEditorModeSettingsModified, UObject*, FProperty*);
	UModelingToolsEditorModeSettingsModified OnModified;

	virtual void PostEditChangeProperty(FPropertyChangedEvent& PropertyChangedEvent) override
	{
		OnModified.Broadcast(this, PropertyChangedEvent.Property);

		Super::PostEditChangeProperty(PropertyChangedEvent);
	}

	//
	// Restrictive Mode
	//

	virtual bool InRestrictiveMode() const
	{
		return bRestrictiveMode;
	}

	virtual void SetRestrictiveMode(bool bEnabled)
	{
		bRestrictiveMode = bEnabled;
	}

	virtual FString GetRestrictiveModeAutoGeneratedAssetPath() const
	{
		return bRestrictiveMode ? RestrictiveModeAutoGeneratedAssetPath : FString();
	}

	virtual bool SetRestrictiveModeAutoGeneratedAssetPath(const FString& AssetPath)
	{
		if (bRestrictiveMode)
		{
			RestrictiveModeAutoGeneratedAssetPath = AssetPath;
			return true;
		}
		return false;
	}

protected:

	/** Assets auto-generated by Modeling Tools are stored at this path when in restrictive mode, relative to the package folder path. */
	UPROPERTY(Config)
	FString RestrictiveModeAutoGeneratedAssetPath = TEXT("Meshes");

	bool bRestrictiveMode = false;




public:
	//
	// Settings that are currently stored during a single session but are not stored in the config file
	// (may promote them to persistent settings in the future)
	//

	/** Toggle Absolute World Grid Position snapping */
	UPROPERTY(Transient)
	bool bEnableAbsoluteWorldSnapping = false;
};




/**
 * Defines a color to be used for a particular Tool Palette Section
 */
USTRUCT()
struct FModelingModeCustomSectionColor
{
	GENERATED_BODY()

	/** Name of Section in Modeling Mode Tool Palette */
	UPROPERTY(EditAnywhere, Category = "SectionColor")
	FString SectionName = TEXT("");

	/** Custom Header Color */
	UPROPERTY(EditAnywhere, Category = "SectionColor")
	FLinearColor Color = FLinearColor::Gray;
};


/**
 * Defines a color to be used for a particular Tool Palette Tool
 */
USTRUCT()
struct FModelingModeCustomToolColor
{
	GENERATED_BODY()

	/**
	 * Name of Section or Tool in Modeling Mode Tool Palette
	 *
	 * Format:
	 * SectionName        (Specifies a default color for all tools in the section.)
	 * SectionName.ToolName        (Specifies an override color for a specific tool in the given section.)
	 */
	UPROPERTY(EditAnywhere, Category = "ToolColor")
	FString ToolName = TEXT("");

	/** Custom Tool Color */
	UPROPERTY(EditAnywhere, Category = "ToolColor")
	FLinearColor Color = FLinearColor::Gray;
};


/**
 * Defines a Named list/set of content-browser Collection names
 */
USTRUCT()
struct FModelingModeAssetCollectionSet
{
	GENERATED_BODY()

	/** Name of the set of collections */
	UPROPERTY(EditAnywhere, Category = "CollectionSet")
	FString Name;

	/** List of Collection names */
	UPROPERTY(EditAnywhere, Category = "CollectionSet")
	TArray<FCollectionReference> Collections;
};


UCLASS(config=Editor)
class MODELINGTOOLSEDITORMODE_API UModelingToolsModeCustomizationSettings : public UDeveloperSettings
{
	GENERATED_BODY()

public:
	// UDeveloperSettings overrides

	virtual FName GetContainerName() const { return FName("Editor"); }
	virtual FName GetCategoryName() const { return FName("Plugins"); }
	virtual FName GetSectionName() const { return FName("ModelingMode"); }

	virtual FText GetSectionText() const override;
	virtual FText GetSectionDescription() const override;

public:

	/** Toggle between the Legacy Modeling Mode Palette and the new UI (requires exiting and re-entering the Mode) */
	UPROPERTY(config, EditAnywhere, Category = "Modeling Mode|UI Customization")
	bool bUseLegacyModelingPalette = false;

	/** Add the names of Modeling Mode Tool Palette Sections to have them appear at the top of the Tool Palette, in the order listed below. */
	UPROPERTY(config, EditAnywhere, Category = "Modeling Mode|UI Customization")
	TArray<FString> ToolSectionOrder;

	/** Tool Names listed in the array below will appear in a Favorites section at the top of the Modeling Mode Tool Palette */
	UE_DEPRECATED(5.3, "Modeling Mode favorites are now set through FEditablePalette or the Mode UI itself")
	TArray<FString> ToolFavorites;
	
	/** Custom Section Header Colors for listed Sections in the Modeling Mode Tool Palette */
	UPROPERTY(config, EditAnywhere, Category = "Modeling Mode|UI Customization")
	TArray<FModelingModeCustomSectionColor> SectionColors;

	/**
	 * Custom Tool Colors for listed Tools in the Modeling Mode Tool Palette.
	 * 
	 * Format:
	 * SectionName        (Specifies a default color for all tools in the section.)
	 * SectionName.ToolName        (Specifies an override color for a specific tool in the given section.)
	 */
	UPROPERTY(config, EditAnywhere, Category = "Modeling Mode|UI Customization")
	TArray<FModelingModeCustomToolColor> ToolColors;


	/**
	 * A Brush Alpha Set is a named list of Content Browser Collections, which will be shown as a separate tab in 
	 * the Texture Asset Picker used in various Modeling Mode Tools when selecting a Brush Alpha (eg in Sculpting)
	 */
	UPROPERTY(config, EditAnywhere, Category = "Modeling Mode|Tool Assets")
	TArray<FModelingModeAssetCollectionSet> BrushAlphaSets;


	/**
	 * If true, the category labels will be shown on the toolbar buttons, else they will be hidden
	 */
	UPROPERTY(config, EditAnywhere, Category = "Modeling Mode|UI Customization")
	bool bShowCategoryButtonLabels = true;
	
	/**
	 * If true, Tool buttons will always be shown when in a Tool. By default they will be hidden.
	 */
	UPROPERTY(config, EditAnywhere, Category = "Modeling Mode|UI Customization")
	bool bAlwaysShowToolButtons = false;

	/**
	 * Custom color for when geometry is not a part of current selection when using Mesh Element Selection
	 */
	UPROPERTY(Config, EditAnywhere, Category = "Modeling Mode|Mesh Element Selection Mode")
	FLinearColor UnselectedColor = FLinearColor(.068478f, 0.56478f, 0.708376f,.5f); // default light blue

	/**
	 * Custom color hovering over selected geometry in Mesh Element Selection
	 */
	UPROPERTY(Config, EditAnywhere, Category = "Modeling Mode|Mesh Element Selection Mode")
	FLinearColor HoverOverSelectedColor = FLinearColor(1.0f, 0.943014f, 0.74f); // default white

	/**
	* Custom color hovering over unselected geometry in Mesh Element Selection
	*/
	UPROPERTY(Config, EditAnywhere, Category = "Modeling Mode|Mesh Element Selection Mode")
	FLinearColor HoverOverUnselectedColor = FLinearColor(1.0f, 0.25f, 0.712328f, .5f); // default pink

	/**
	* Custom color for when geometry is a part of the current selection when using Mesh Element Selection
	*/
	UPROPERTY(Config, EditAnywhere, Category = "Modeling Mode|Mesh Element Selection Mode")
	FLinearColor GeometrySelectedColor = FLinearColor(0.982251f, 0.672443f, 0.029557f, .5f); // default yellow

public:

	// saved-state for various mode settings that are configured via UI toggles/etc, and not exposed in settings dialog

	UPROPERTY(config)
	int32 LastMeshSelectionDragMode = 0;

	UPROPERTY(config)
	int32 LastMeshSelectionLocalFrameMode = 0;
	
	UPROPERTY(config)
	bool bMeshSelectionHitBackFaces = true;

public:

	// saved-state for various mode settings that does not persist between editor runs

	UPROPERTY()
	int32 LastMeshSelectionElementType = 0;

	UPROPERTY()
	int32 LastMeshSelectionTopologyMode = 0;

	UPROPERTY()
	bool bLastMeshSelectionVolumeToggle = true;

	UPROPERTY()
	bool bLastMeshSelectionStaticMeshToggle = true;


};
