<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>NVIDIA Aftermath</Name>
  <Location>/Engine/Source/ThirdParty/NVIDIA/nvaftermath</Location>
  <Date>2017-03-29T15:15:27.1263592-04:00</Date>
  <Function>GPU Crash Diagnostics</Function>
  <Justification>To fix GPU crashes from users</Justification>
  <Eula>Custom agreement between NVIDIA/Epic</Eula>
  <RedistributeTo>
    <EndUserGroup>Licensees</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>/Engine/Source/ThirdParty/Licenses/Aftermath_NVAPI_License.txt</LicenseFolder>
</TpsData>